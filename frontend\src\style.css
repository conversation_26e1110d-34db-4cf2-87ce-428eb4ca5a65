/* 现代化设计系统 */
:root {
  /* 字体系统 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  line-height: 1.6;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 基础色彩系统 */
  color-scheme: light dark;
  
  /* 自定义阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --gradient-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 动画时长 */
  --duration-fast: 0.15s;
  --duration-normal: 0.25s;
  --duration-slow: 0.35s;
  
  /* 圆角系统 */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 48px;
  
  /* 毛玻璃效果 */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-backdrop: blur(10px);
}

/* 暗色主题优化 */
@media (prefers-color-scheme: dark) {
  :root {
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: var(--el-bg-color-page);
  transition: background-color var(--duration-normal) ease;
}

/* 链接样式 */
a {
  font-weight: 500;
  color: var(--el-color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) ease;
}

a:hover {
  color: var(--el-color-primary-light-3);
}

/* 按钮样式增强 */
button {
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 14px;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all var(--duration-fast) ease;
  position: relative;
  overflow: hidden;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

button:active {
  transform: translateY(0);
}

/* 卡片样式增强 */
.modern-card {
  background: var(--el-bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--el-border-color-lighter);
  transition: all var(--duration-normal) ease;
  position: relative;
  overflow: hidden;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* 毛玻璃面板 */
.glass-panel {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

/* 渐变文本 */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  /* 备用色彩方案，确保在不支持渐变的情况下有合理的显示 */
  color: var(--el-color-primary);
  /* 增强文本的清晰度 */
  position: relative;
  z-index: 1;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
}

.status-indicator.success {
  background: var(--el-color-success);
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.status-indicator.warning {
  background: var(--el-color-warning);
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
}

.status-indicator.info {
  background: var(--el-color-info);
  box-shadow: 0 0 0 2px rgba(144, 147, 153, 0.2);
}

.status-indicator.pulse::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  border-radius: 50%;
  background: inherit;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.5);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 加载动画增强 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--el-border-color-light);
  border-radius: 50%;
  border-top-color: var(--el-color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 平滑滚动 */
* {
  scroll-behavior: smooth;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: var(--radius-sm);
  transition: background var(--duration-fast) ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 响应式字体 */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--el-text-color-primary);
  margin: 0;
}

h1 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
}

h2 {
  font-size: clamp(1.25rem, 3vw, 2rem);
}

h3 {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
}

/* 应用容器 */
#app {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* 工具类 */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-hover {
  transition: box-shadow var(--duration-normal) ease;
}

.shadow-hover:hover {
  box-shadow: var(--shadow-lg);
}

.transform-hover {
  transition: transform var(--duration-fast) ease;
}

.transform-hover:hover {
  transform: translateY(-2px);
}
