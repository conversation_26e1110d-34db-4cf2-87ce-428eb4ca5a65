[project]
name = "text-annotation"
version = "0.1.0"
description = "基于 FastAPI 的文本标注和标记后端系统"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.115.12",
    "pydantic>=2.11.5",
    "pyyaml>=6.0.2",
    "sqlalchemy>=2.0.41",
    "uvicorn[standard]>=0.34.3",
    "loguru>=0.7.0",
    "requests>=2.31.0", # demo脚本需要
    "tqdm>=4.67.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "ruff>=0.6.0",
]

[project.scripts]
# 服务器启动命令
text-annotation-server = "app.main:main"
server = "app.main:main"

# 数据导入命令
text-annotation-import = "scripts.run_import:main"
import-data = "scripts.run_import:main"

# API演示命令
text-annotation-demo = "scripts.demo:main"
demo = "scripts.demo:main"

# 数据库迁移命令
migrate-db = "scripts.migrate_db:main"
db-migrate = "scripts.migrate_db:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app", "scripts"]
