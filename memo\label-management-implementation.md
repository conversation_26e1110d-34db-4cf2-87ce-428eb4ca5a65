# 标签管理功能实现文档

## 概述

本次实现了完整的标签可视化和管理界面，包括标签列表展示、统计图表、新增标签、删除标签等功能。

## 实现的功能

### 1. 增强的 Label Store

**文件**: `frontend/src/stores/label.ts`

**新增功能**:
- 系统统计数据管理
- 搜索和过滤功能
- 标签使用频率排序
- 未使用标签识别
- 统计概览计算

**主要方法**:
- `fetchSystemStats()` - 获取系统统计数据
- `setSearchQuery()` - 设置搜索查询
- `getLabelStats()` - 获取单个标签统计
- `initializeData()` - 初始化所有数据

### 2. 标签管理主页面

**文件**: `frontend/src/pages/LabelManagePage.vue`

**功能特性**:
- 统计概览卡片（总标签数、已使用、未使用、已标注文本）
- 搜索和过滤功能
- 标签排序（按使用频率、名称、创建时间）
- 标签网格展示
- 统计图表展示
- 响应式设计

### 3. 标签卡片组件

**文件**: `frontend/src/components/label/LabelCard.vue`

**功能特性**:
- 标签基本信息展示
- 使用次数和状态显示
- 使用频率进度条
- 删除操作
- 查看使用详情（预留功能）

### 4. 新增标签对话框

**文件**: `frontend/src/components/label/CreateLabelDialog.vue`

**功能特性**:
- 表单验证（名称重复检查）
- 标签名称和描述输入
- 创建提示和说明
- 错误处理

### 5. 标签统计图表

**文件**: `frontend/src/components/label/LabelStatsChart.vue`

**功能特性**:
- 柱状图和饼图切换
- 显示数量控制
- 交互式图表
- 统计摘要信息
- 基于 ECharts 实现

## 路由配置

**文件**: `frontend/src/router/index.ts`

新增路由:
```javascript
{
  path: '/label-manage',
  name: 'LabelManage',
  component: () => import('@/pages/LabelManagePage.vue'),
  meta: {
    title: '标签管理'
  }
}
```

## 导航集成

**文件**: `frontend/src/pages/HomePage.vue`

- 修改了"管理标签"操作项，从 `loadLabels` 改为 `goToLabelManage`
- 添加了 `goToLabelManage()` 方法跳转到标签管理页面

## 依赖添加

**新增依赖**:
- `echarts` - 图表库
- `vue-echarts` - Vue 3 ECharts 组件

## 使用方法

### 访问标签管理

1. **从首页**: 点击"管理标签"操作卡片
2. **直接访问**: 浏览器访问 `/label-manage`

### 主要操作

1. **查看标签统计**: 页面顶部显示统计概览
2. **搜索标签**: 使用搜索框过滤标签
3. **排序和筛选**: 使用下拉菜单选择排序方式和筛选条件
4. **新增标签**: 点击"新增标签"按钮
5. **删除标签**: 点击标签卡片的操作菜单
6. **查看图表**: 页面底部显示使用频率图表

### 图表功能

- **柱状图**: 显示标签使用频率对比
- **饼图**: 显示标签使用分布
- **显示控制**: 可选择显示前5/10/20个或全部标签
- **统计摘要**: 显示最常用标签、平均使用次数、使用率

## 技术特点

1. **响应式设计**: 适配桌面和移动设备
2. **现代化UI**: 使用 Element Plus 组件库
3. **数据可视化**: ECharts 图表展示
4. **状态管理**: Pinia store 统一管理
5. **TypeScript**: 完整的类型定义
6. **组件化**: 模块化组件设计

## 后续优化建议

1. **标签编辑功能**: 支持修改标签名称
2. **批量操作**: 支持批量删除标签
3. **标签分类**: 支持标签分组管理
4. **使用详情**: 点击标签查看具体使用的文本
5. **导入导出**: 支持标签配置的导入导出
6. **标签模板**: 预设常用标签模板

## 测试建议

1. **功能测试**: 测试所有CRUD操作
2. **界面测试**: 测试响应式布局
3. **数据测试**: 测试大量标签的性能
4. **交互测试**: 测试图表交互功能
5. **错误处理**: 测试网络错误等异常情况

## 注意事项

1. 删除标签前会显示确认对话框
2. 标签名称不能重复
3. 图表数据实时更新
4. 搜索功能支持模糊匹配
5. 未使用的标签会有特殊标识 