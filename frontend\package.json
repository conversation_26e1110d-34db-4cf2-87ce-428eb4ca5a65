{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5178", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "pinia": "^3.0.3", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^22.15.29", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "code-inspector-plugin": "^0.20.12", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}